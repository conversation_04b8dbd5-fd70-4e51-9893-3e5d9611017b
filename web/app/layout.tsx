import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import Script from 'next/script';
import './globals.css';
import { AnalyticsProvider } from '../components/AnalyticsProvider';
import { RollbarErrorBoundary } from '../components/RollbarErrorBoundary';
import GoogleAdsense from '../components/GoogleAdsense';
import { GoogleTagManager } from '@next/third-parties/google';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Buat Surat Lamaran secara Instan menggunakan AI',
  description: 'Manfaatkan kecanggihan AI untuk membuat surat lamaran kerja secara otomatis. Perbesar peluang dilirik oleh HR!',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="id">
      <head>
        <meta name="google-adsense-account" content={`${process.env.NEXT_PUBLIC_GOOGLE_ADS_CLIENT_ID}`} />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <noscript>
          <img
            height="1"
            width="1"
            style={{display: 'none'}}
            src="https://www.facebook.com/tr?id=****************&ev=PageView&noscript=1"
            alt=""
          />
        </noscript>
        <GoogleTagManager gtmId='GTM-N375ZS5H' />
        <GoogleAdsense />
        <RollbarErrorBoundary>
          <AnalyticsProvider>
            {children}
          </AnalyticsProvider>
        </RollbarErrorBoundary>
      </body>
    </html>
  );
}
