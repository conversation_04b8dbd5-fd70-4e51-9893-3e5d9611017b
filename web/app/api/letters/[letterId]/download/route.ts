export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { captureApiError } from '@/utils/errorMonitoring';
import puppeteer from 'puppeteer-core';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';

// Maximum duration for the API route
export const maxDuration = 30; // 30 seconds

/**
 * Check if a PDF exists in Supabase storage for the given letter ID
 */
async function checkPdfExists(supabase: any, letterId: string): Promise<boolean> {
  try {
    const filePath = `${letterId}.pdf`;
    const { data, error } = await supabase.storage
      .from('generated-letters')
      .list('', { search: filePath });

    if (error) {
      console.error('Error checking PDF existence:', error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking PDF existence:', error);
    return false;
  }
}

/**
 * Download PDF from Supabase storage
 */
async function downloadPdfFromStorage(supabase: any, letterId: string): Promise<Uint8Array | null> {
  try {
    const filePath = `${letterId}.pdf`;
    const { data, error } = await supabase.storage
      .from('generated-letters')
      .download(filePath);

    if (error) {
      console.error('Error downloading PDF from storage:', error);
      return null;
    }

    return new Uint8Array(await data.arrayBuffer());
  } catch (error) {
    console.error('Error downloading PDF from storage:', error);
    return null;
  }
}

/**
 * Upload PDF to Supabase storage
 */
async function uploadPdfToStorage(supabase: any, letterId: string, pdfBuffer: Uint8Array): Promise<boolean> {
  try {
    const filePath = `${letterId}.pdf`;
    const { error } = await supabase.storage
      .from('generated-letters')
      .upload(filePath, pdfBuffer, {
        contentType: 'application/pdf',
        upsert: true, // Replace if exists
      });

    if (error) {
      console.error('Error uploading PDF to storage:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error uploading PDF to storage:', error);
    return false;
  }
}

/**
 * Generate PDF from letter data using Puppeteer
 */
async function generatePdf(letter: any): Promise<Uint8Array> {
  const browser = await puppeteer.connect({
    browserWSEndpoint: `wss://production-sfo.browserless.io?token=${process.env.BROWSERLESS_IO_TOKEN}`
  });

  const page = await browser.newPage();

  try {
    // Set content to the page
    await page.setContent(letter.design_html);

    // Wait for all fonts to be loaded
    await page.evaluateHandle('document.fonts.ready');

    // Set page size to A4
    await page.setViewport({
      width: 794, // A4 width in pixels (72 dpi)
      height: 1123, // A4 height in pixels (72 dpi)
      deviceScaleFactor: 2, // Higher scale for better quality
    });

    // Generate PDF
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0',
        right: '0',
        bottom: '0',
        left: '0',
      },
    });

    return pdf;
  } finally {
    // Always close the browser
    await browser.close();
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ letterId: string }> }
) {
  let letterId: string | undefined;

  try {
    const resolvedParams = await params;
    letterId = resolvedParams.letterId;

    if (!letterId) {
      return NextResponse.json({
        error: 'Letter ID is required'
      }, { status: 400 });
    }

    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    if (!accessToken) {
      return NextResponse.json({
        error: 'Authentication required'
      }, { status: 401 });
    }

    // Create Supabase client
    const supabase = await createClient();

    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return NextResponse.json({
        error: 'Invalid authentication token'
      }, { status: 401 });
    }

    // Get the letter from database
    const { data: letter, error: fetchError } = await supabase
      .from('letters')
      .select('id, user_id, plain_text, design_html, template_id, created_at')
      .eq('id', letterId)
      .eq('user_id', user.id) // Ensure user can only access their own letters
      .single();

    if (fetchError || !letter) {
      console.error('Error fetching letter:', fetchError);
      return NextResponse.json({
        error: 'Letter not found or access denied'
      }, { status: 404 });
    }

    // Check if PDF already exists in storage
    const pdfExists = await checkPdfExists(supabase, letterId);
    let pdfBuffer: Uint8Array;

    try {
      if (pdfExists) {
        // Download existing PDF from storage
        console.log(`PDF exists for letter ${letterId}, downloading from storage`);
        const existingPdf = await downloadPdfFromStorage(supabase, letterId);

        if (existingPdf) {
          pdfBuffer = existingPdf;
        } else {
          // If download fails, fall back to generating new PDF
          console.log(`Failed to download existing PDF for letter ${letterId}, generating new one`);
          pdfBuffer = await generatePdf(letter);
          // Try to upload the newly generated PDF
          await uploadPdfToStorage(supabase, letterId, pdfBuffer);
        }
      } else {
        // Generate new PDF
        console.log(`PDF does not exist for letter ${letterId}, generating new one`);
        pdfBuffer = await generatePdf(letter);
        // Upload to storage for future use
        const uploadSuccess = await uploadPdfToStorage(supabase, letterId, pdfBuffer);
        if (!uploadSuccess) {
          console.warn(`Failed to upload PDF to storage for letter ${letterId}`);
        }
      }
    } catch (pdfError) {
      console.error('PDF generation/retrieval error:', pdfError);
      captureApiError('pdf-generation', pdfError as Error, {
        letterId: letterId
      });
      return NextResponse.json({
        error: 'Failed to generate PDF. Please try downloading as HTML or text instead.'
      }, { status: 500 });
    }

    // Get template information for filename
    const selectedTemplate = letter.template_id ? getTemplateById(letter.template_id) : undefined;

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="Surat_Lamaran_Gigsta_${selectedTemplate?.name}.pdf"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
    
  } catch (error) {
    console.error('Error downloading letter:', error);
    
    // Capture error details with Rollbar
    captureApiError('download-letter', error, {
      requestUrl: request.url,
      letterId: letterId
    });
    
    return NextResponse.json({
      success: false,
      error: 'Failed to download letter. Please try again.'
    }, { status: 500 });
  }
}