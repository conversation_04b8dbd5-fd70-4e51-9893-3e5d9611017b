"use client";

import { FC, useCallback, useEffect, useRef, useState } from "react";
import { useShadowDOM } from "@/hooks/useShadowDOM";

interface ResumePreviewProps {
  htmlContent: string;
  onOverflowDetected?: (isOverflowing: boolean) => void;
}

const ResumePreview: FC<ResumePreviewProps> = ({ htmlContent, onOverflowDetected }) => {
  const shadowContainerRef = useShadowDOM({ html: htmlContent });

  // Zoom state management
  const [zoomLevel, setZoomLevel] = useState<number>(100);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [scrollOffset, setScrollOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Refs for touch gesture handling
  const containerRef = useRef<HTMLDivElement>(null);
  const lastTouchDistance = useRef<number>(0);
  const isGesturing = useRef<boolean>(false);

  // Overflow detection state
  const [isOverflowing, setIsOverflowing] = useState<boolean>(false);
  const overflowCheckTimer = useRef<NodeJS.Timeout | null>(null);
  
  // Zoom constraints
  const MIN_ZOOM = 10;
  const MAX_ZOOM = 200;
  const ZOOM_STEP = 10;

  // Zoom control functions
  const zoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + ZOOM_STEP, MAX_ZOOM));
  }, []);

  const zoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - ZOOM_STEP, MIN_ZOOM));
  }, []);

  const resetZoom = useCallback(() => {
    setZoomLevel(100);
    setScrollOffset({ x: 0, y: 0 });
  }, []);

  // Touch gesture handlers for pinch-to-zoom
  const getTouchDistance = (touches: React.TouchList): number => {
    if (touches.length < 2) return 0;
    const touch1 = touches[0];
    const touch2 = touches[1];
    return Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    );
  };

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 2) {
      e.preventDefault();
      isGesturing.current = true;
      lastTouchDistance.current = getTouchDistance(e.touches);
    } else if (e.touches.length === 1 && zoomLevel > 100) {
      // Enable dragging when zoomed in
      setIsDragging(true);
      setDragStart({
        x: e.touches[0].clientX - scrollOffset.x,
        y: e.touches[0].clientY - scrollOffset.y
      });
    }
  }, [zoomLevel, scrollOffset]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (e.touches.length === 2 && isGesturing.current) {
      e.preventDefault();
      const currentDistance = getTouchDistance(e.touches);
      if (lastTouchDistance.current > 0) {
        const scale = currentDistance / lastTouchDistance.current;
        const newZoom = Math.min(Math.max(zoomLevel * scale, MIN_ZOOM), MAX_ZOOM);
        setZoomLevel(newZoom);
      }
      lastTouchDistance.current = currentDistance;
    } else if (e.touches.length === 1 && isDragging && zoomLevel > 100) {
      e.preventDefault();
      setScrollOffset({
        x: e.touches[0].clientX - dragStart.x,
        y: e.touches[0].clientY - dragStart.y
      });
    }
  }, [zoomLevel, isDragging, dragStart]);

  const handleTouchEnd = useCallback(() => {
    isGesturing.current = false;
    lastTouchDistance.current = 0;
    setIsDragging(false);
  }, []);

  // Mouse wheel zoom support
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -ZOOM_STEP : ZOOM_STEP;
      setZoomLevel(prev => Math.min(Math.max(prev + delta, MIN_ZOOM), MAX_ZOOM));
    }
  }, []);

  // Overflow detection function
  const checkForOverflow = useCallback(() => {
    if (!shadowContainerRef.current || !htmlContent) return;

    // Clear existing timer
    if (overflowCheckTimer.current) {
      clearTimeout(overflowCheckTimer.current);
    }

    // Debounce the overflow check to avoid excessive calculations
    overflowCheckTimer.current = setTimeout(() => {
      try {
        const container = shadowContainerRef.current;
        if (!container) return;

        // Check if shadow DOM is being used
        const shadowRoot = container.shadowRoot;
        let resumeContainer: Element | null = null;

        if (shadowRoot) {
          // Shadow DOM is available
          resumeContainer = shadowRoot.querySelector('.resume-container');
          if (!resumeContainer) {
            resumeContainer = shadowRoot.querySelector('body') || shadowRoot.querySelector('html') || shadowRoot.firstElementChild;
          }
        } else {
          // Fallback to regular DOM (when shadow DOM is not supported)
          resumeContainer = container.querySelector('.resume-container');
          if (!resumeContainer) {
            // If no resume-container, use the container itself
            resumeContainer = container;
          }
        }

        if (!resumeContainer) return;

        // A4 dimensions in pixels at 96 DPI (standard web DPI)
        // 210mm = 794px, 297mm = 1123px
        const A4_HEIGHT_PX = 1123;

        // Get the actual content height
        const contentHeight = resumeContainer.scrollHeight;

        // Check if content overflows the A4 page height
        const overflowing = contentHeight > A4_HEIGHT_PX;

        // Update state if overflow status changed
        if (overflowing !== isOverflowing) {
          setIsOverflowing(overflowing);
          onOverflowDetected?.(overflowing);
        }
      } catch (error) {
        console.warn('Error checking for overflow:', error);
      }
    }, 300); // 300ms debounce
  }, [htmlContent, isOverflowing, onOverflowDetected]);

  // Check for overflow when content changes
  useEffect(() => {
    if (htmlContent) {
      // Longer delay to ensure shadow DOM is fully rendered and styled
      setTimeout(checkForOverflow, 500);
    }
  }, [htmlContent, checkForOverflow]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === '0') {
        e.preventDefault();
        resetZoom();
      } else if ((e.ctrlKey || e.metaKey) && e.key === '=') {
        e.preventDefault();
        zoomIn();
      } else if ((e.ctrlKey || e.metaKey) && e.key === '-') {
        e.preventDefault();
        zoomOut();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [zoomIn, zoomOut, resetZoom]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (overflowCheckTimer.current) {
        clearTimeout(overflowCheckTimer.current);
      }
    };
  }, []);

  return (
    <div className="w-fit max-w-full mx-auto bg-gray-100 rounded-lg">
      {/* Zoom Controls */}
      <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200 rounded-t-lg">
        <div className="flex items-center gap-2">
          <button
            onClick={zoomOut}
            disabled={zoomLevel <= MIN_ZOOM}
            className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-manipulation"
            title="Zoom Out (Ctrl+-)"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          
          <div className="flex items-center justify-center min-w-[60px] sm:min-w-[80px] px-2 py-1 text-xs sm:text-sm font-medium bg-white border border-gray-300 rounded-lg">
            {zoomLevel}%
          </div>
          
          <button
            onClick={zoomIn}
            disabled={zoomLevel >= MAX_ZOOM}
            className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors touch-manipulation"
            title="Zoom In (Ctrl+=)"
          >
            <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </div>
        
        <button
          onClick={resetZoom}
          className="px-3 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors touch-manipulation"
          title="Reset Zoom (Ctrl+0)"
        >
          Reset
        </button>
      </div>

      {/* A4 Paper Container */}
      <div
        ref={containerRef}
        className="relative w-fit max-w-full bg-gray-100 shadow-lg overflow-auto"
        style={{
          touchAction: zoomLevel > 100 ? 'none' : 'auto'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onWheel={handleWheel}
      >
        {/* Zoomable Content Container */}
        <div
          className="relative origin-top-left transition-transform duration-200 ease-out bg-white shadow-sm"
          style={{
            transform: `scale(${zoomLevel / 100}) translate(${scrollOffset.x}px, ${scrollOffset.y}px)`,
            cursor: zoomLevel > 100 ? (isDragging ? 'grabbing' : 'grab') : 'default'
          }}
        >
          {/* Always render the container div to maintain the ref */}
          <div ref={shadowContainerRef} className="text-left" />
          
          {/* Show fallback message only when no content */}
          {!htmlContent && (
            <div className="flex items-center justify-center h-96 w-96">
              <span className="text-gray-400">Pratinjau belum tersedia</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResumePreview;
