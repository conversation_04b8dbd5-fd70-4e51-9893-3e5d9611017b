import { ResumeTemplate } from './resume-templates/resumeTemplates';
import { StructuredResumeData } from '../types/resume-structured';
import { convertToTemplateData, validateTemplateData, ResumeTemplateData } from '../types/template-data';

// Import precompiled templates
import cleanProfessionalTemplate from './handlebars-templates/clean-professional.hbs';
import modernCleanTemplate from './handlebars-templates/modern-clean.hbs';
import classicProfessionalTemplate from './handlebars-templates/classic-professional.hbs';

// Import and register Handlebars helpers
import * as Handlebars from 'handlebars/runtime';
import * as helpers from './handlebars-helpers/index.js';

/**
 * Template compilation cache for performance
 * Key: template ID, Value: precompiled Handlebars template
 */
const precompiledTemplates = new Map<string, any>();

// Register all Handlebars helpers
Object.keys(helpers).forEach(helperName => {
  Handlebars.registerHelper(helperName, (helpers as any)[helperName]);
});

// Initialize precompiled templates
precompiledTemplates.set('clean-professional', cleanProfessionalTemplate);
precompiledTemplates.set('modern-clean', modernCleanTemplate);
precompiledTemplates.set('classic-professional', classicProfessionalTemplate);

/**
 * Get precompiled template by ID
 * @param templateId - Template ID
 * @returns Precompiled Handlebars template or null if not found
 */
function getPrecompiledTemplate(templateId: string): any {
  const template = precompiledTemplates.get(templateId);
  if (!template) {
    throw new Error(`Precompiled template not found for ID: ${templateId}`);
  }
  return template;
}

/**
 * Validate template data and provide detailed error information
 * @param templateData - Template data to validate
 * @returns Validation result with details
 */
function validateTemplateDataWithDetails(templateData: ResumeTemplateData): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Validate required fields
  const validation = validateTemplateData(templateData);
  if (!validation.isValid) {
    errors.push(...validation.missingFields.map(field => `Missing required field: ${field}`));
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Main function to fill resume template with structured data
 * @param template - Resume template object
 * @param data - Structured resume data
 * @returns Filled HTML string
 * @throws Error if template compilation fails or data validation fails
 */
export function fillResumeTemplate(template: ResumeTemplate, data: StructuredResumeData): string {
  try {
    // Convert structured data to template format
    const templateData = convertToTemplateData(data);
    
    // Validate template data
    const validation = validateTemplateDataWithDetails(templateData);
    
    if (!validation.isValid) {
      throw new Error(`Template data validation failed: ${validation.errors.join(', ')}`);
    }
    
    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn('Template data warnings:', validation.warnings);
    }
    
    // Get precompiled template
    const precompiledTemplate = getPrecompiledTemplate(template.id);
    
    // Generate HTML with additional error context
    let html: string;
    try {
      html = precompiledTemplate(templateData);
    } catch (renderError) {
      const renderErrorMessage = renderError instanceof Error ? renderError.message : 'Unknown render error';
      throw new Error(`Template rendering failed: ${renderErrorMessage}. This might be due to missing or malformed data in the template.`);
    }
    
    // Basic validation of generated HTML
    if (!html || html.trim().length === 0) {
      throw new Error('Generated HTML is empty');
    }
    
    if (templateData.name && !html.includes(templateData.name)) {
      throw new Error('Generated HTML does not contain the candidate name');
    }
    
    return html;
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    // Log error for debugging with more context
    console.error('Template fill error:', {
      templateId: template.id,
      templateName: template.name,
      error: errorMessage,
      candidateName: data?.personalInfo?.fullName || 'Unknown',
      dataStructure: {
        hasPersonalInfo: !!data?.personalInfo,
        hasExperiences: !!data?.experiences && Array.isArray(data.experiences),
        experiencesCount: data?.experiences?.length || 0,
        hasEducation: !!data?.education && Array.isArray(data.education),
        educationCount: data?.education?.length || 0,
        hasSkills: !!data?.skills?.categories && Array.isArray(data.skills.categories),
        skillsCount: data?.skills?.categories?.length || 0,
      }
    });
    
    throw new Error(`Failed to fill template "${template.name}": ${errorMessage}`);
  }
}

/**
 * Get available precompiled template IDs
 */
export function getAvailableTemplateIds(): string[] {
  return Array.from(precompiledTemplates.keys());
}

/**
 * Check if a template is available
 */
export function isTemplateAvailable(templateId: string): boolean {
  return precompiledTemplates.has(templateId);
}

/**
 * Test template compilation with sample data
 * @param template - Template to test
 * @param sampleData - Sample structured resume data
 * @returns Test result
 */
export function testTemplate(template: ResumeTemplate, sampleData: StructuredResumeData): {
  success: boolean;
  error?: string;
  warnings?: string[];
  htmlLength?: number;
} {
  try {
    const html = fillResumeTemplate(template, sampleData);
    const templateData = convertToTemplateData(sampleData);
    const validation = validateTemplateDataWithDetails(templateData);
    
    return {
      success: true,
      warnings: validation.warnings,
      htmlLength: html.length
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}